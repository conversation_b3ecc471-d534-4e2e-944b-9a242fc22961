#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考研搜网站爬虫 - 增强版
使用webdriver-manager自动管理Chrome驱动
支持多年份数据爬取和数据分析
"""

import time
import json
import csv
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re
import os
from datetime import datetime


class KaoYanSouCrawlerEnhanced:
    def __init__(self, headless=True):
        """初始化爬虫"""
        self.setup_driver(headless)
        self.data = []
        self.basic_info = {}
        
    def setup_driver(self, headless=True):
        """设置Chrome浏览器驱动，自动下载驱动"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_argument('--ignore-ssl-errors')
        chrome_options.add_argument('--ignore-certificate-errors-spki-list')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        try:
            print("自动下载Chrome驱动...")
            # 使用webdriver-manager自动下载和管理Chrome驱动
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            print("✓ 自动驱动设置成功")
        except Exception as e:
            print(f"自动下载Chrome驱动失败: {e}")
            print("尝试使用手动指定的驱动路径...")

            # 尝试常见的驱动路径
            possible_paths = [
                r"C:\Users\<USER>\Downloads\chromedriver-win64\chromedriver-win64\chromedriver.exe",
                r"C:\chromedriver\chromedriver.exe",
                r".\chromedriver.exe",
                "chromedriver.exe"
            ]

            driver_found = False
            for path in possible_paths:
                try:
                    if os.path.exists(path):
                        service = Service(path)
                        self.driver = webdriver.Chrome(service=service, options=chrome_options)
                        print(f"✓ 成功使用手动指定的驱动: {path}")
                        driver_found = True
                        break
                except Exception as path_error:
                    continue

            if not driver_found:
                print("❌ 无法找到Chrome驱动，请手动下载并放置在正确位置")
                raise Exception("Chrome驱动设置失败")

        # 执行脚本来隐藏webdriver特征
        try:
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        except:
            pass

        self.wait = WebDriverWait(self.driver, 20)
        
    def extract_basic_info(self):
        """提取基本信息：学校名、专业名、专业编号"""
        try:
            # 等待页面完全加载
            print("等待页面加载...")
            time.sleep(8)

            # 尝试多种方式等待页面加载完成
            try:
                # 等待主要内容区域加载
                self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".info")))
                print("✓ 页面主要内容已加载")
            except TimeoutException:
                print("⚠️ 页面加载超时，尝试继续...")

            # 尝试多种选择器提取学校名
            school_name = ""
            school_selectors = [
                ".sch-name span:first-child",
                ".sch-name span:first-of-type",
                ".sch-name > span:first-child",
                ".right .sch-name span:first-child"
            ]

            for selector in school_selectors:
                try:
                    school_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    school_name = school_element.text.strip()
                    if school_name:
                        print(f"✓ 找到学校名: {school_name}")
                        break
                except:
                    continue

            # 尝试多种选择器提取专业信息
            major_name = ""
            major_code = ""
            major_selectors = [
                ".sch-name .major",
                ".sch-name span.major",
                ".right .sch-name .major"
            ]

            for selector in major_selectors:
                try:
                    major_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in major_elements:
                        text = element.text.strip()
                        if '(' in text and ')' in text:
                            print(f"✓ 找到专业信息: {text}")
                            # 使用正则表达式提取专业名和编号
                            match = re.match(r'(.+?)\((\d+)\)', text)
                            if match:
                                major_name = match.group(1).strip()
                                major_code = match.group(2).strip()
                                break
                            else:
                                major_name = text
                    if major_name:
                        break
                except:
                    continue

            # 如果还是没有找到，尝试从页面标题或其他位置提取
            if not school_name or not major_name:
                print("尝试从页面其他位置提取信息...")
                try:
                    # 从页面标题提取
                    page_title = self.driver.title
                    print(f"页面标题: {page_title}")

                    # 打印页面源码的一部分用于调试
                    page_source = self.driver.page_source[:2000]
                    print("页面源码片段:")
                    print(page_source)

                except Exception as debug_error:
                    print(f"调试信息获取失败: {debug_error}")

            self.basic_info = {
                'school_name': school_name,
                'major_name': major_name,
                'major_code': major_code
            }

            return self.basic_info
        except Exception as e:
            print(f"提取基本信息失败: {e}")
            # 打印更详细的错误信息
            import traceback
            traceback.print_exc()
            return {
                'school_name': '',
                'major_name': '',
                'major_code': ''
            }
    
    def get_available_years(self):
        """获取可用的年份列表"""
        try:
            # 等待年份选择器加载
            print("正在查找年份选择器...")
            time.sleep(3)

            # 尝试多种选择器找到年份下拉框
            year_selectors = [
                ".el-select .el-input__inner",
                ".el-select input",
                "input[placeholder*='年份']",
                "input[placeholder*='选择年份']",
                ".header .el-select input"
            ]

            year_select = None
            for selector in year_selectors:
                try:
                    year_select = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if year_select.is_displayed():
                        print(f"✓ 找到年份选择器: {selector}")
                        break
                except:
                    continue

            if not year_select:
                print("⚠️ 未找到年份选择器，使用默认年份")
                return ['2024']

            # 点击年份选择下拉框
            try:
                self.driver.execute_script("arguments[0].click();", year_select)
                time.sleep(3)
                print("✓ 年份下拉框已点击")
            except Exception as click_error:
                print(f"点击年份选择器失败: {click_error}")
                return ['2024']

            # 尝试多种选择器获取年份选项
            years = []
            option_selectors = [
                ".el-select-dropdown__item",
                ".el-select-dropdown .el-select-dropdown__item",
                ".el-popper .el-select-dropdown__item",
                "li[role='option']"
            ]

            for selector in option_selectors:
                try:
                    year_options = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if year_options:
                        print(f"✓ 找到年份选项: {len(year_options)} 个")
                        for option in year_options:
                            try:
                                year_text = option.text.strip()
                                if year_text and year_text.isdigit() and len(year_text) == 4:
                                    years.append(year_text)
                                    print(f"  - {year_text}")
                            except:
                                continue
                        break
                except:
                    continue

            # 点击空白处关闭下拉框
            try:
                self.driver.find_element(By.TAG_NAME, "body").click()
                time.sleep(2)
            except:
                pass

            # 如果没有找到年份，返回默认年份
            if not years:
                print("⚠️ 未找到任何年份选项，使用默认年份")
                years = ['2024']

            return sorted(list(set(years)), reverse=True)  # 去重并按年份降序排列
        except Exception as e:
            print(f"获取年份列表失败: {e}")
            import traceback
            traceback.print_exc()
            return ['2024']  # 默认返回2024年
    
    def select_year(self, year):
        """选择指定年份"""
        try:
            print(f"正在选择年份: {year}")
            
            # 点击年份选择下拉框
            year_select = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".el-select .el-input__inner"))
            )
            self.driver.execute_script("arguments[0].click();", year_select)
            time.sleep(2)
            
            # 查找并点击指定年份
            year_options = self.wait.until(
                EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".el-select-dropdown__item"))
            )
            
            for option in year_options:
                if option.text.strip() == str(year):
                    self.driver.execute_script("arguments[0].click();", option)
                    time.sleep(4)  # 等待数据加载
                    return True
            
            print(f"未找到年份 {year}")
            return False
        except Exception as e:
            print(f"选择年份 {year} 失败: {e}")
            return False
    
    def extract_table_data(self, year):
        """提取表格数据"""
        try:
            # 等待表格加载
            time.sleep(3)
            
            # 检查是否有数据
            try:
                table_rows = self.wait.until(
                    EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".el-table__body tbody tr"))
                )
            except TimeoutException:
                print(f"{year}年暂无数据")
                return []
            
            year_data = []
            scores = []
            
            for i, row in enumerate(table_rows):
                try:
                    cells = row.find_elements(By.CSS_SELECTOR, "td .cell")
                    if len(cells) >= 9:  # 确保有足够的列
                        # 提取初试成绩（第9列，索引为8）
                        score_text = cells[8].text.strip()
                        if score_text and score_text.replace('.', '').isdigit():
                            score = float(score_text)
                            scores.append(score)
                            
                            row_data = {
                                'school_name': self.basic_info['school_name'],
                                'major_name': self.basic_info['major_name'],
                                'major_code': self.basic_info['major_code'],
                                'year': year,
                                'politics_score': cells[1].text.strip(),
                                'foreign_lang_score': cells[2].text.strip(),
                                'subject1_score': cells[3].text.strip(),
                                'subject2_score': cells[4].text.strip(),
                                'student_name': cells[5].text.strip(),
                                'first_choice_school': cells[6].text.strip(),
                                'total_score': cells[7].text.strip(),
                                'initial_test_score': score,
                                'retest_score': cells[9].text.strip() if len(cells) > 9 else '',
                                'special_plan': cells[10].text.strip() if len(cells) > 10 else '',
                                'data_type': 'detail'
                            }
                            year_data.append(row_data)
                except Exception as e:
                    print(f"处理第{i+1}行数据失败: {e}")
                    continue
            
            # 添加统计信息
            if scores:
                min_score = min(scores)
                max_score = max(scores)
                avg_score = sum(scores) / len(scores)
                
                summary_data = {
                    'school_name': self.basic_info['school_name'],
                    'major_name': self.basic_info['major_name'],
                    'major_code': self.basic_info['major_code'],
                    'year': year,
                    'min_initial_test_score': min_score,
                    'max_initial_test_score': max_score,
                    'avg_initial_test_score': round(avg_score, 2),
                    'total_students': len(scores),
                    'data_type': 'summary'
                }
                year_data.append(summary_data)
                
                print(f"{year}年数据: 最低分{min_score}, 最高分{max_score}, 平均分{avg_score:.2f}, 录取人数{len(scores)}")
            
            return year_data
        except Exception as e:
            print(f"提取{year}年表格数据失败: {e}")
            return []
    
    def crawl_data(self, url):
        """爬取数据主函数"""
        try:
            print(f"正在访问: {url}")
            self.driver.get(url)
            time.sleep(5)
            
            # 提取基本信息
            basic_info = self.extract_basic_info()
            print(f"学校: {basic_info['school_name']}")
            print(f"专业: {basic_info['major_name']} ({basic_info['major_code']})")
            
            # 获取可用年份
            years = self.get_available_years()
            print(f"可用年份: {years}")
            
            # 爬取每个年份的数据
            for year in years:
                print(f"\n正在爬取 {year} 年数据...")
                if self.select_year(year):
                    year_data = self.extract_table_data(year)
                    self.data.extend(year_data)
                    detail_count = len([d for d in year_data if d.get('data_type') == 'detail'])
                    print(f"{year} 年数据爬取完成，共 {detail_count} 条详细记录")
                else:
                    print(f"{year} 年数据爬取失败")
                
                time.sleep(2)  # 避免请求过快
            
            total_details = len([d for d in self.data if d.get('data_type') == 'detail'])
            total_summaries = len([d for d in self.data if d.get('data_type') == 'summary'])
            print(f"\n总共爬取 {total_details} 条详细记录，{total_summaries} 条汇总记录")
            return self.data
            
        except Exception as e:
            print(f"爬取数据失败: {e}")
            return []
    
    def save_to_csv(self, filename=None):
        """保存数据到CSV文件"""
        if not self.data:
            print("没有数据可保存")
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'kaoyansou_data_{timestamp}.csv'
        
        # 分别保存详细数据和汇总数据
        detail_data = [d for d in self.data if d.get('data_type') == 'detail']
        summary_data = [d for d in self.data if d.get('data_type') == 'summary']
        
        if detail_data:
            detail_filename = filename.replace('.csv', '_details.csv')
            df_detail = pd.DataFrame(detail_data)
            df_detail.to_csv(detail_filename, index=False, encoding='utf-8-sig')
            print(f"详细数据已保存到 {detail_filename}")
        
        if summary_data:
            summary_filename = filename.replace('.csv', '_summary.csv')
            df_summary = pd.DataFrame(summary_data)
            df_summary.to_csv(summary_filename, index=False, encoding='utf-8-sig')
            print(f"汇总数据已保存到 {summary_filename}")
    
    def save_to_json(self, filename=None):
        """保存数据到JSON文件"""
        if not self.data:
            print("没有数据可保存")
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'kaoyansou_data_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.data, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"数据已保存到 {filename}")
    
    def print_summary(self):
        """打印数据摘要"""
        if not self.data:
            print("没有数据可显示")
            return
        
        print("\n=== 数据摘要 ===")
        summary_data = [item for item in self.data if item.get('data_type') == 'summary']
        
        if summary_data:
            for summary in sorted(summary_data, key=lambda x: x['year'], reverse=True):
                print(f"{summary['year']}年 {summary['school_name']} {summary['major_name']}:")
                print(f"  初试最低分: {summary['min_initial_test_score']}")
                print(f"  初试最高分: {summary['max_initial_test_score']}")
                print(f"  初试平均分: {summary['avg_initial_test_score']}")
                print(f"  录取人数: {summary['total_students']}")
                print()
    
    def close(self):
        """关闭浏览器"""
        if hasattr(self, 'driver'):
            self.driver.quit()


def main():
    """主函数"""
    url = "https://www.kaoyansou.cn/f/sc/index.html#/school/10003/detail?majorOutRelationId=578447"
    
    # 创建爬虫实例，headless=False可以看到浏览器操作过程
    crawler = KaoYanSouCrawlerEnhanced(headless=True)
    
    try:
        # 爬取数据
        data = crawler.crawl_data(url)
        
        if data:
            # 保存数据
            crawler.save_to_csv()
            crawler.save_to_json()
            
            # 打印摘要信息
            crawler.print_summary()
        else:
            print("未能获取到任何数据")
        
    except Exception as e:
        print(f"程序执行失败: {e}")
    finally:
        crawler.close()


if __name__ == "__main__":
    main()
