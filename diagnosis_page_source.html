<html lang=""><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><link rel="icon" href="favicon.png"><title>研可岸择校</title><script src="https://hm.baidu.com/hm.js?c4448e00599076c214159936a85fc61b"></script><script defer="defer" src="js/chunk-vendors.6191f411.js"></script><script defer="defer" src="js/app.ab2ab762.js"></script><link href="css/chunk-vendors.8b7d4c1a.css" rel="stylesheet"><link href="css/app.67e92673.css" rel="stylesheet"><link rel="stylesheet" type="text/css" href="css/813.45d0bab2.css"></head><body class="" style="--el-color-primary: #ff7214;"><noscript><strong>We're sorry but kaoyan doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><div id="app" data-v-app=""><div data-v-4d197f42="" class="home"><div data-v-4d197f42="" class="top"><img data-v-4d197f42="" class="logo" src="img/logos.7caf05c8.png"><div data-v-4d197f42="" class="login"><button data-v-4d197f42="" aria-disabled="false" type="button" class="el-button el-button--primary" style="--el-button-bg-color: #ff7214; --el-button-text-color: var(--el-color-black); --el-button-border-color: #ff7214; --el-button-hover-bg-color: rgb(255, 156, 91); --el-button-hover-text-color: var(--el-color-black); --el-button-hover-border-color: rgb(255, 156, 91); --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">登录</span></button></div></div><div data-v-4d197f42="" class="main"><ul data-v-4d197f42="" role="menubar" class="el-menu el-menu--horizontal el-menus" style="--el-menu-active-color: #ff7214; --el-menu-level: 0;"><li data-v-4d197f42="" class="el-menu-item is-active" role="menuitem" tabindex="0">院校库</li><li data-v-4d197f42="" class="el-menu-item" role="menuitem" tabindex="0">专业库</li><li data-v-4d197f42="" class="el-menu-item" role="menuitem" tabindex="0">AI择校</li><li data-v-4d197f42="" class="el-menu-item" role="menuitem" tabindex="0">同考试科目</li><li data-v-4d197f42="" class="el-sub-menu" role="menuitem" ariahaspopup="true" aria-expanded="false" tabindex="0"><div class="el-sub-menu__title el-tooltip__trigger el-tooltip__trigger" style="border-bottom-color: transparent;">工具箱<i class="el-icon el-sub-menu__icon-arrow" style="transform: none;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path></svg></i></div></li><li data-v-4d197f42="" class="el-menu-item" role="menuitem" tabindex="0"><div data-v-4d197f42="" class="el-badge item">9.9元答疑班<sup class="el-badge__content el-badge__content--danger is-fixed is-dot"></sup></div></li></ul><div data-v-4d197f42="" class="container"><div data-v-214740f3="" data-v-4d197f42="" class="school"><div data-v-214740f3="" class="header"><div data-v-214740f3="" class="el-input el-input--large el-input--suffix"><!-- input --><!-- prepend slot --><!--v-if--><div class="el-input__wrapper" tabindex="-1"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" autocomplete="off" tabindex="0" placeholder="学校名称" id="el-id-3882-27"><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--></div><button data-v-214740f3="" aria-disabled="false" type="button" class="el-button el-button--primary el-button--large" style="--el-button-bg-color: #ff7214; --el-button-text-color: var(--el-color-black); --el-button-border-color: #ff7214; --el-button-hover-bg-color: rgb(255, 156, 91); --el-button-hover-text-color: var(--el-color-black); --el-button-hover-border-color: rgb(255, 156, 91); --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">搜索</span></button></div><div data-v-214740f3="" class="con"><div data-v-214740f3="" class="filter"><div data-v-214740f3="" class="item"><div data-v-214740f3="" class="left"><div data-v-214740f3="" class="title">院校位置</div><img data-v-214740f3="" class="arrow" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAAKlBMVEUAAACnp6unp6uoqKvMzMynp6unp6umpqumpqunp6yoqKympqunp6ympqpJwijVAAAADXRSTlMAeLBNBYaF5tpaQZtrYEkNwQAAAEVJREFUGNNjQAOMPUgc9rsCSDJ3LyJJxSJLsZIlxYYilYsqdRPBYem9hOA43b2FkNC9a4AkcZlaEtwgCUQYGCA4nFowFgDuGSBrHF3fiQAAAABJRU5ErkJggg=="></div><div data-v-214740f3="" class="center active" style="font-size: 14px; cursor: pointer;"> 全部 </div><div data-v-214740f3="" class="pro-right"><div data-v-214740f3="" class="a-con"><div data-v-214740f3="" class="a-left">A区</div><div data-v-214740f3="" class="a-right"><div data-v-214740f3="" class="ele">北京</div><div data-v-214740f3="" class="ele">天津</div><div data-v-214740f3="" class="ele">河北</div><div data-v-214740f3="" class="ele">河南</div><div data-v-214740f3="" class="ele">山西</div><div data-v-214740f3="" class="ele">辽宁</div><div data-v-214740f3="" class="ele">吉林</div><div data-v-214740f3="" class="ele">黑龙江</div><div data-v-214740f3="" class="ele">上海</div><div data-v-214740f3="" class="ele">江苏</div><div data-v-214740f3="" class="ele">浙江</div><div data-v-214740f3="" class="ele">安徽</div><div data-v-214740f3="" class="ele">福建</div><div data-v-214740f3="" class="ele">江西</div><div data-v-214740f3="" class="ele">山东</div><div data-v-214740f3="" class="ele">湖北</div><div data-v-214740f3="" class="ele">湖南</div><div data-v-214740f3="" class="ele">广东</div><div data-v-214740f3="" class="ele">四川</div><div data-v-214740f3="" class="ele">重庆</div><div data-v-214740f3="" class="ele">陕西</div></div></div><div data-v-214740f3="" class="a-con"><div data-v-214740f3="" class="a-left">B区</div><div data-v-214740f3="" class="a-right"><div data-v-214740f3="" class="ele">内蒙古</div><div data-v-214740f3="" class="ele">广西</div><div data-v-214740f3="" class="ele">海南</div><div data-v-214740f3="" class="ele">贵州</div><div data-v-214740f3="" class="ele">云南</div><div data-v-214740f3="" class="ele">西藏</div><div data-v-214740f3="" class="ele">甘肃</div><div data-v-214740f3="" class="ele">青海</div><div data-v-214740f3="" class="ele">宁夏</div><div data-v-214740f3="" class="ele">新疆</div></div></div></div></div><div data-v-214740f3="" class="item"><div data-v-214740f3="" class="left"><div data-v-214740f3="" class="title">学校层次</div><img data-v-214740f3="" class="arrow" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAAKlBMVEUAAACnp6unp6uoqKvMzMynp6unp6umpqumpqunp6yoqKympqunp6ympqpJwijVAAAADXRSTlMAeLBNBYaF5tpaQZtrYEkNwQAAAEVJREFUGNNjQAOMPUgc9rsCSDJ3LyJJxSJLsZIlxYYilYsqdRPBYem9hOA43b2FkNC9a4AkcZlaEtwgCUQYGCA4nFowFgDuGSBrHF3fiQAAAABJRU5ErkJggg=="></div><div data-v-214740f3="" class="right"><div data-v-214740f3="" class="ele active"> 全部 </div><div data-v-214740f3="" class="ele">985</div><div data-v-214740f3="" class="ele">211</div><div data-v-214740f3="" class="ele">普通高校</div></div></div></div><div data-v-46e69b98="" class="item"><img data-v-46e69b98="" class="school-logo" src="https://yanxinsheng-resources.oss-cn-beijing.aliyuncs.com/images_xxxh/10001.jpg"><div data-v-46e69b98="" class="center"><div data-v-46e69b98="" class="name"><!----><div data-v-46e69b98="" class="sn">北京大学</div><!----><!----><!----><!----><span data-v-46e69b98="" class="el-tag el-tag--danger el-tag--light tag"><span class="el-tag__content">A区</span><!--v-if--></span></div><!----><div data-v-46e69b98="" class="desc"><div data-v-46e69b98="" class="info"><span data-v-46e69b98="" class="el-tag el-tag--light" style="background-color: rgb(255, 242, 232); color: rgb(135, 20, 0); cursor: pointer;"><span class="el-tag__content">985</span><!--v-if--></span></div></div></div><div data-v-46e69b98="" class="right"><button data-v-46e69b98="" aria-disabled="false" type="button" class="el-button el-button--primary is-plain" style="--el-button-bg-color: rgb(255, 241, 232); --el-button-text-color: #ff7214; --el-button-border-color: rgb(255, 185, 138); --el-button-hover-text-color: var(--el-color-white); --el-button-hover-bg-color: #ff7214; --el-button-hover-border-color: #ff7214; --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-text-color: var(--el-color-white); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">查看专业</span></button><!----></div></div><div data-v-46e69b98="" class="item"><img data-v-46e69b98="" class="school-logo" src="https://yanxinsheng-resources.oss-cn-beijing.aliyuncs.com/images_xxxh/10002.jpg"><div data-v-46e69b98="" class="center"><div data-v-46e69b98="" class="name"><!----><div data-v-46e69b98="" class="sn">中国人民大学</div><!----><!----><!----><!----><span data-v-46e69b98="" class="el-tag el-tag--danger el-tag--light tag"><span class="el-tag__content">A区</span><!--v-if--></span></div><!----><div data-v-46e69b98="" class="desc"><div data-v-46e69b98="" class="info"><span data-v-46e69b98="" class="el-tag el-tag--light" style="background-color: rgb(255, 242, 232); color: rgb(135, 20, 0); cursor: pointer;"><span class="el-tag__content">985</span><!--v-if--></span></div></div></div><div data-v-46e69b98="" class="right"><button data-v-46e69b98="" aria-disabled="false" type="button" class="el-button el-button--primary is-plain" style="--el-button-bg-color: rgb(255, 241, 232); --el-button-text-color: #ff7214; --el-button-border-color: rgb(255, 185, 138); --el-button-hover-text-color: var(--el-color-white); --el-button-hover-bg-color: #ff7214; --el-button-hover-border-color: #ff7214; --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-text-color: var(--el-color-white); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">查看专业</span></button><!----></div></div><div data-v-46e69b98="" class="item"><img data-v-46e69b98="" class="school-logo" src="https://yanxinsheng-resources.oss-cn-beijing.aliyuncs.com/images_xxxh/10003.jpg"><div data-v-46e69b98="" class="center"><div data-v-46e69b98="" class="name"><!----><div data-v-46e69b98="" class="sn">清华大学</div><!----><!----><!----><!----><span data-v-46e69b98="" class="el-tag el-tag--danger el-tag--light tag"><span class="el-tag__content">A区</span><!--v-if--></span></div><!----><div data-v-46e69b98="" class="desc"><div data-v-46e69b98="" class="info"><span data-v-46e69b98="" class="el-tag el-tag--light" style="background-color: rgb(255, 242, 232); color: rgb(135, 20, 0); cursor: pointer;"><span class="el-tag__content">985</span><!--v-if--></span></div></div></div><div data-v-46e69b98="" class="right"><button data-v-46e69b98="" aria-disabled="false" type="button" class="el-button el-button--primary is-plain" style="--el-button-bg-color: rgb(255, 241, 232); --el-button-text-color: #ff7214; --el-button-border-color: rgb(255, 185, 138); --el-button-hover-text-color: var(--el-color-white); --el-button-hover-bg-color: #ff7214; --el-button-hover-border-color: #ff7214; --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-text-color: var(--el-color-white); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">查看专业</span></button><!----></div></div><div data-v-46e69b98="" class="item"><img data-v-46e69b98="" class="school-logo" src="https://yanxinsheng-resources.oss-cn-beijing.aliyuncs.com/images_xxxh/10004.jpg"><div data-v-46e69b98="" class="center"><div data-v-46e69b98="" class="name"><!----><div data-v-46e69b98="" class="sn">北京交通大学</div><!----><!----><!----><!----><span data-v-46e69b98="" class="el-tag el-tag--danger el-tag--light tag"><span class="el-tag__content">A区</span><!--v-if--></span></div><!----><div data-v-46e69b98="" class="desc"><div data-v-46e69b98="" class="info"><span data-v-46e69b98="" class="el-tag el-tag--light" style="background-color: rgb(254, 252, 224); color: rgb(211, 84, 0); cursor: pointer;"><span class="el-tag__content">211</span><!--v-if--></span></div></div></div><div data-v-46e69b98="" class="right"><button data-v-46e69b98="" aria-disabled="false" type="button" class="el-button el-button--primary is-plain" style="--el-button-bg-color: rgb(255, 241, 232); --el-button-text-color: #ff7214; --el-button-border-color: rgb(255, 185, 138); --el-button-hover-text-color: var(--el-color-white); --el-button-hover-bg-color: #ff7214; --el-button-hover-border-color: #ff7214; --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-text-color: var(--el-color-white); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">查看专业</span></button><!----></div></div><div data-v-46e69b98="" class="item"><img data-v-46e69b98="" class="school-logo" src="https://yanxinsheng-resources.oss-cn-beijing.aliyuncs.com/images_xxxh/10005.jpg"><div data-v-46e69b98="" class="center"><div data-v-46e69b98="" class="name"><!----><div data-v-46e69b98="" class="sn">北京工业大学</div><!----><!----><!----><!----><span data-v-46e69b98="" class="el-tag el-tag--danger el-tag--light tag"><span class="el-tag__content">A区</span><!--v-if--></span></div><!----><div data-v-46e69b98="" class="desc"><div data-v-46e69b98="" class="info"><span data-v-46e69b98="" class="el-tag el-tag--light" style="background-color: rgb(254, 252, 224); color: rgb(211, 84, 0); cursor: pointer;"><span class="el-tag__content">211</span><!--v-if--></span></div></div></div><div data-v-46e69b98="" class="right"><button data-v-46e69b98="" aria-disabled="false" type="button" class="el-button el-button--primary is-plain" style="--el-button-bg-color: rgb(255, 241, 232); --el-button-text-color: #ff7214; --el-button-border-color: rgb(255, 185, 138); --el-button-hover-text-color: var(--el-color-white); --el-button-hover-bg-color: #ff7214; --el-button-hover-border-color: #ff7214; --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-text-color: var(--el-color-white); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">查看专业</span></button><!----></div></div><div data-v-46e69b98="" class="item"><img data-v-46e69b98="" class="school-logo" src="https://yanxinsheng-resources.oss-cn-beijing.aliyuncs.com/images_xxxh/10006.jpg"><div data-v-46e69b98="" class="center"><div data-v-46e69b98="" class="name"><!----><div data-v-46e69b98="" class="sn">北京航空航天大学</div><!----><!----><!----><!----><span data-v-46e69b98="" class="el-tag el-tag--danger el-tag--light tag"><span class="el-tag__content">A区</span><!--v-if--></span></div><!----><div data-v-46e69b98="" class="desc"><div data-v-46e69b98="" class="info"><span data-v-46e69b98="" class="el-tag el-tag--light" style="background-color: rgb(255, 242, 232); color: rgb(135, 20, 0); cursor: pointer;"><span class="el-tag__content">985</span><!--v-if--></span></div></div></div><div data-v-46e69b98="" class="right"><button data-v-46e69b98="" aria-disabled="false" type="button" class="el-button el-button--primary is-plain" style="--el-button-bg-color: rgb(255, 241, 232); --el-button-text-color: #ff7214; --el-button-border-color: rgb(255, 185, 138); --el-button-hover-text-color: var(--el-color-white); --el-button-hover-bg-color: #ff7214; --el-button-hover-border-color: #ff7214; --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-text-color: var(--el-color-white); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">查看专业</span></button><!----></div></div><div data-v-46e69b98="" class="item"><img data-v-46e69b98="" class="school-logo" src="https://yanxinsheng-resources.oss-cn-beijing.aliyuncs.com/images_xxxh/10007.jpg"><div data-v-46e69b98="" class="center"><div data-v-46e69b98="" class="name"><!----><div data-v-46e69b98="" class="sn">北京理工大学</div><!----><!----><!----><!----><span data-v-46e69b98="" class="el-tag el-tag--danger el-tag--light tag"><span class="el-tag__content">A区</span><!--v-if--></span></div><!----><div data-v-46e69b98="" class="desc"><div data-v-46e69b98="" class="info"><span data-v-46e69b98="" class="el-tag el-tag--light" style="background-color: rgb(255, 242, 232); color: rgb(135, 20, 0); cursor: pointer;"><span class="el-tag__content">985</span><!--v-if--></span></div></div></div><div data-v-46e69b98="" class="right"><button data-v-46e69b98="" aria-disabled="false" type="button" class="el-button el-button--primary is-plain" style="--el-button-bg-color: rgb(255, 241, 232); --el-button-text-color: #ff7214; --el-button-border-color: rgb(255, 185, 138); --el-button-hover-text-color: var(--el-color-white); --el-button-hover-bg-color: #ff7214; --el-button-hover-border-color: #ff7214; --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-text-color: var(--el-color-white); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">查看专业</span></button><!----></div></div><div data-v-46e69b98="" class="item"><img data-v-46e69b98="" class="school-logo" src="https://yanxinsheng-resources.oss-cn-beijing.aliyuncs.com/images_xxxh/10008.jpg"><div data-v-46e69b98="" class="center"><div data-v-46e69b98="" class="name"><!----><div data-v-46e69b98="" class="sn">北京科技大学</div><!----><!----><!----><!----><span data-v-46e69b98="" class="el-tag el-tag--danger el-tag--light tag"><span class="el-tag__content">A区</span><!--v-if--></span></div><!----><div data-v-46e69b98="" class="desc"><div data-v-46e69b98="" class="info"><span data-v-46e69b98="" class="el-tag el-tag--light" style="background-color: rgb(254, 252, 224); color: rgb(211, 84, 0); cursor: pointer;"><span class="el-tag__content">211</span><!--v-if--></span></div></div></div><div data-v-46e69b98="" class="right"><button data-v-46e69b98="" aria-disabled="false" type="button" class="el-button el-button--primary is-plain" style="--el-button-bg-color: rgb(255, 241, 232); --el-button-text-color: #ff7214; --el-button-border-color: rgb(255, 185, 138); --el-button-hover-text-color: var(--el-color-white); --el-button-hover-bg-color: #ff7214; --el-button-hover-border-color: #ff7214; --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-text-color: var(--el-color-white); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">查看专业</span></button><!----></div></div><div data-v-46e69b98="" class="item"><img data-v-46e69b98="" class="school-logo" src="https://yanxinsheng-resources.oss-cn-beijing.aliyuncs.com/images_xxxh/10009.jpg"><div data-v-46e69b98="" class="center"><div data-v-46e69b98="" class="name"><!----><div data-v-46e69b98="" class="sn">北方工业大学</div><!----><!----><!----><!----><span data-v-46e69b98="" class="el-tag el-tag--danger el-tag--light tag"><span class="el-tag__content">A区</span><!--v-if--></span></div><!----><div data-v-46e69b98="" class="desc"><div data-v-46e69b98="" class="info"><span data-v-46e69b98="" class="el-tag el-tag--light" style="background-color: rgb(181, 245, 236); color: rgb(0, 71, 79); cursor: pointer;"><span class="el-tag__content">普通高校</span><!--v-if--></span></div></div></div><div data-v-46e69b98="" class="right"><button data-v-46e69b98="" aria-disabled="false" type="button" class="el-button el-button--primary is-plain" style="--el-button-bg-color: rgb(255, 241, 232); --el-button-text-color: #ff7214; --el-button-border-color: rgb(255, 185, 138); --el-button-hover-text-color: var(--el-color-white); --el-button-hover-bg-color: #ff7214; --el-button-hover-border-color: #ff7214; --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-text-color: var(--el-color-white); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">查看专业</span></button><!----></div></div><div data-v-46e69b98="" class="item"><img data-v-46e69b98="" class="school-logo" src="https://yanxinsheng-resources.oss-cn-beijing.aliyuncs.com/images_xxxh/10010.jpg"><div data-v-46e69b98="" class="center"><div data-v-46e69b98="" class="name"><!----><div data-v-46e69b98="" class="sn">北京化工大学</div><!----><!----><!----><!----><span data-v-46e69b98="" class="el-tag el-tag--danger el-tag--light tag"><span class="el-tag__content">A区</span><!--v-if--></span></div><!----><div data-v-46e69b98="" class="desc"><div data-v-46e69b98="" class="info"><span data-v-46e69b98="" class="el-tag el-tag--light" style="background-color: rgb(254, 252, 224); color: rgb(211, 84, 0); cursor: pointer;"><span class="el-tag__content">211</span><!--v-if--></span></div></div></div><div data-v-46e69b98="" class="right"><button data-v-46e69b98="" aria-disabled="false" type="button" class="el-button el-button--primary is-plain" style="--el-button-bg-color: rgb(255, 241, 232); --el-button-text-color: #ff7214; --el-button-border-color: rgb(255, 185, 138); --el-button-hover-text-color: var(--el-color-white); --el-button-hover-bg-color: #ff7214; --el-button-hover-border-color: #ff7214; --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-text-color: var(--el-color-white); --el-button-active-border-color: rgb(208, 95, 20);"><!--v-if--><span class="">查看专业</span></button><!----></div></div><div class="el-overlay" style="z-index: 2021; display: none;"><div role="dialog" aria-modal="true" aria-label="院校专业对比" aria-describedby="el-id-3882-33" class="el-overlay-dialog"></div></div><div class="el-overlay" style="z-index: 2022; display: none;"><div role="dialog" aria-modal="true" aria-labelledby="el-id-3882-34" aria-describedby="el-id-3882-35" class="el-overlay-dialog"></div></div><div data-v-dd73457a="" data-v-214740f3="" class="el-pagination is-background pagination"><span class="el-pagination__total is-first" disabled="false">共 929 条</span><span class="el-pagination__sizes"><div class="el-select el-select--default"><div class="select-trigger el-tooltip__trigger el-tooltip__trigger"><!--v-if--><!-- fix: https://github.com/element-plus/element-plus/issues/11415 --><!--v-if--><div class="el-input el-input--default el-input--suffix"><!-- input --><!-- prepend slot --><!--v-if--><div class="el-input__wrapper" tabindex="-1"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" readonly="" autocomplete="off" tabindex="0" placeholder="请选择" id="el-id-3882-37"><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><i class="el-icon el-select__caret el-select__icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path></svg></i><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--></div></div></div></span><button type="button" class="btn-prev" disabled="" aria-label="上一页" aria-disabled="true"><i class="el-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M609.408 149.376 277.76 489.6a32 32 0 0 0 0 44.672l331.648 340.352a29.12 29.12 0 0 0 41.728 0 30.592 30.592 0 0 0 0-42.752L339.264 511.936l311.872-319.872a30.592 30.592 0 0 0 0-42.688 29.12 29.12 0 0 0-41.728 0z"></path></svg></i></button><ul class="el-pager"><li class="is-active number" aria-current="true" aria-label="第 1 页" tabindex="0"> 1 </li><!--v-if--><li class="number" aria-current="false" aria-label="第 2 页" tabindex="0">2</li><li class="number" aria-current="false" aria-label="第 3 页" tabindex="0">3</li><li class="number" aria-current="false" aria-label="第 4 页" tabindex="0">4</li><li class="number" aria-current="false" aria-label="第 5 页" tabindex="0">5</li><li class="number" aria-current="false" aria-label="第 6 页" tabindex="0">6</li><li class="more btn-quicknext el-icon" tabindex="0" aria-label="向后 5 页"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"></path></svg></li><li class="number" aria-current="false" aria-label="第 93 页" tabindex="0">93</li></ul><button type="button" class="btn-next" aria-label="下一页" aria-disabled="false"><i class="el-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M340.864 149.312a30.592 30.592 0 0 0 0 42.752L652.736 512 340.864 831.872a30.592 30.592 0 0 0 0 42.752 29.12 29.12 0 0 0 41.728 0L714.24 534.336a32 32 0 0 0 0-44.672L382.592 149.376a29.12 29.12 0 0 0-41.728 0z"></path></svg></i></button><span class="el-pagination__jump is-last" disabled="false"><span class="el-pagination__goto">前往</span><div class="el-input el-input--default el-pagination__editor is-in-pagination"><!-- input --><!-- prepend slot --><!--v-if--><div class="el-input__wrapper" tabindex="-1"><!-- prefix slot --><!--v-if--><input class="el-input__inner" min="1" max="93" type="number" autocomplete="off" tabindex="0" aria-label="页" id="el-id-3882-38"><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--></div><span class="el-pagination__classifier">页</span></span></div></div></div></div><div data-v-4d197f42="" class="footer"><div data-v-4d197f42=""><div data-v-4d197f42="" class="infos" style="margin-top: 10px;"><span data-v-4d197f42="" class="left">关于我们</span><span data-v-4d197f42="" class="left">联系我们</span><span data-v-4d197f42="" class="left">浙ICP备2023002559号</span><span data-v-4d197f42="" class="left">友情链接: 考研调剂模拟系统</span></div></div></div></div><div class="el-overlay" style="z-index: 2019;"><div role="dialog" aria-modal="true" aria-labelledby="el-id-3882-28" aria-describedby="el-id-3882-29" class="el-overlay-dialog"><div class="el-dialog my-dialog" tabindex="-1" style="--el-dialog-width: 320px;"><header class="el-dialog__header"><span role="heading" class="el-dialog__title"></span><!--v-if--></header><div id="el-id-3882-29" class="el-dialog__body"><div data-v-4d197f42="" class="login-form"><img data-v-4d197f42="" class="img" src="https://ykn-senior.oss-cn-beijing.aliyuncs.com/images/yankean-qrcode.jpg"><div data-v-4d197f42="" class="tips"> 扫码关注公众号【<span data-v-4d197f42="" class="active">研可岸</span>】回复『<span data-v-4d197f42="" class="active">登录码</span>』获取 </div><div data-v-4d197f42="" class="el-input el-input--prefix code"><!-- input --><!-- prepend slot --><!--v-if--><div class="el-input__wrapper" tabindex="-1"><!-- prefix slot --><span class="el-input__prefix"><span class="el-input__prefix-inner"><img data-v-4d197f42="" src="img/code.a2e91163.svg" style="width: 24px; height: 24px;"><!--v-if--></span></span><input class="el-input__inner" type="text" autocomplete="off" tabindex="0" placeholder="请输入6位动态码" id="el-id-3882-31"><!-- suffix slot --><!--v-if--></div><!-- append slot --><!--v-if--></div><div data-v-4d197f42="" class="el-select code" style="width: 100%;"><div class="select-trigger el-tooltip__trigger el-tooltip__trigger"><!--v-if--><!-- fix: https://github.com/element-plus/element-plus/issues/11415 --><!--v-if--><div class="el-input el-input--suffix"><!-- input --><!-- prepend slot --><!--v-if--><div class="el-input__wrapper" tabindex="-1"><!-- prefix slot --><!--v-if--><input class="el-input__inner" type="text" readonly="" autocomplete="off" tabindex="0" placeholder="报考年份" id="el-id-3882-32"><!-- suffix slot --><span class="el-input__suffix"><span class="el-input__suffix-inner"><i class="el-icon el-select__caret el-select__icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M831.872 340.864 512 652.672 192.128 340.864a30.592 30.592 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.592 30.592 0 0 0-42.752 0z"></path></svg></i><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--><!--v-if--></span></span></div><!-- append slot --><!--v-if--></div></div></div><button data-v-4d197f42="" aria-disabled="false" type="button" class="el-button el-button--primary login" style="--el-button-bg-color: #ff7214; --el-button-text-color: var(--el-color-black); --el-button-border-color: #ff7214; --el-button-hover-bg-color: rgb(255, 156, 91); --el-button-hover-text-color: var(--el-color-black); --el-button-hover-border-color: rgb(255, 156, 91); --el-button-active-bg-color: rgb(208, 95, 20); --el-button-active-border-color: rgb(208, 95, 20); color: rgb(255, 255, 255);"><!--v-if--><span class="">登录</span></button><div data-v-4d197f42="" class="agree"><span data-v-4d197f42="">注册登录即表示同意</span><button data-v-4d197f42="" aria-disabled="false" type="button" class="el-button el-button--primary is-link"><!--v-if--><span class="">用户协议</span></button><span data-v-4d197f42="">和</span><button data-v-4d197f42="" aria-disabled="false" type="button" class="el-button el-button--primary is-link"><!--v-if--><span class="">隐私政策</span></button></div><div data-v-4d197f42="" class="agree"><button data-v-4d197f42="" aria-disabled="false" type="button" class="el-button el-button--primary is-link"><!--v-if--><span class="">关于我们</span></button><div data-v-4d197f42="" class="infos" style="font-size: 12px; cursor: pointer; margin-left: 5px;"><span data-v-4d197f42="">浙ICP备2023002559号</span></div></div></div></div><!--v-if--></div></div></div><div class="el-overlay" style="z-index: 2013; display: none;"><div role="dialog" aria-modal="true" aria-labelledby="el-id-3882-17" aria-describedby="el-id-3882-18" class="el-overlay-dialog"></div></div><div data-v-4d197f42="" class="unlock"><div class="el-overlay" style="z-index: 2014; display: none;"><div role="dialog" aria-modal="true" aria-labelledby="el-id-3882-19" aria-describedby="el-id-3882-20" class="el-overlay-dialog" style="display: flex;"></div></div><div data-v-4d197f42="" class="unlock"><div class="el-overlay" style="z-index: 2015; display: none;"><div role="dialog" aria-modal="true" aria-labelledby="el-id-3882-21" aria-describedby="el-id-3882-22" class="el-overlay-dialog" style="display: flex;"></div></div></div></div><div data-v-4d197f42="" class="upload"><div class="el-overlay" style="z-index: 2016; display: none;"><div role="dialog" aria-modal="true" aria-label="查询次数过多" aria-describedby="el-id-3882-23" class="el-overlay-dialog"></div></div></div><div data-v-4d197f42="" class="upload"><div class="el-overlay" style="z-index: 2017; display: none;"><div role="dialog" aria-modal="true" aria-label="研可岸调剂复试" aria-describedby="el-id-3882-24" class="el-overlay-dialog"></div></div></div><div data-v-4d197f42="" class="upload-zhengying"><div class="el-overlay" style="z-index: 2018; display: none;"><div role="dialog" aria-modal="true" aria-labelledby="el-id-3882-25" aria-describedby="el-id-3882-26" class="el-overlay-dialog" style="display: flex;"></div></div></div></div></div><script>var _hmt = _hmt || [];
    (function () {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?c4448e00599076c214159936a85fc61b";
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    })();</script><style>::-webkit-scrollbar {
      /* 针对Webkit内核浏览器的滚动条样式 */
      width: 6px; /* 设置滚动条宽度 */
    }

    ::-webkit-scrollbar-thumb {
      /* 滚动条里面的小方块 */
      background-color: #cccccc; /* 滚动条颜色 */
      border-radius: 10px; /* 滚动条圆角 */
    }

    ::-webkit-scrollbar-track {
      /* 滚动条里面的轨道 */
      background: transparent; /* 轨道颜色 */
    }</style><div id="el-popper-container-3882"><div class="el-popper is-pure is-light" tabindex="-1" aria-hidden="true" role="tooltip" id="el-id-3882-13" data-popper-reference-hidden="false" data-popper-escaped="false" data-popper-placement="bottom-start" style="z-index: 2010; position: absolute; inset: 144px auto auto 666.667px; display: none;"><div class="el-menu--horizontal el-menu--popup-container"><ul class="el-menu el-menu--popup el-menu--popup-bottom-start" style="--el-menu-active-color: #ff7214; --el-menu-level: 1;"><li data-v-4d197f42="" class="el-menu-item" role="menuitem" tabindex="-1" style="font-size: 14px; font-weight: 500;">特殊计划</li><li data-v-4d197f42="" class="el-menu-item" role="menuitem" tabindex="-1" style="font-size: 14px; font-weight: 500;">报考热点</li><li data-v-4d197f42="" class="el-menu-item" role="menuitem" tabindex="-1" style="font-size: 14px; font-weight: 500;">导师评价</li></ul></div><!--v-if--></div><div class="el-popper is-pure is-light el-select__popper" tabindex="-1" aria-hidden="true" style="z-index: 2020; position: absolute; inset: 549.333px auto auto 715.333px; display: none;" role="tooltip" id="el-id-3882-30" data-popper-reference-hidden="false" data-popper-escaped="false" data-popper-placement="bottom-start"><div class="el-select-dropdown" style="min-width: 280px;"><div class="el-scrollbar" style=""><div class="el-select-dropdown__wrap el-scrollbar__wrap el-scrollbar__wrap--hidden-default"><ul class="el-scrollbar__view el-select-dropdown__list" style=""><!--v-if--><li data-v-4d197f42="" class="el-select-dropdown__item"><span>24考研(23年12月初试)</span></li><li data-v-4d197f42="" class="el-select-dropdown__item"><span>25考研(24年12月初试)</span></li><li data-v-4d197f42="" class="el-select-dropdown__item"><span>26考研(25年12月初试)</span></li><li data-v-4d197f42="" class="el-select-dropdown__item"><span>27考研(26年12月初试)</span></li></ul></div><div class="el-scrollbar__bar is-horizontal" style="display: none;"><div class="el-scrollbar__thumb" style="transform: translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display: none;"><div class="el-scrollbar__thumb" style="transform: translateY(0%);"></div></div></div><!--v-if--></div><span class="el-popper__arrow" data-popper-arrow="" style="position: absolute; left: 0px;"></span></div><div class="el-popper is-pure is-light el-select__popper" tabindex="-1" aria-hidden="true" role="tooltip" id="el-id-3882-36" data-popper-reference-hidden="true" data-popper-escaped="true" data-popper-placement="top-start" style="z-index: 2023; position: absolute; inset: auto auto -681.333px 328.667px; display: none;"><div class="el-select-dropdown" style="min-width: 128px;"><div class="el-scrollbar" style=""><div class="el-select-dropdown__wrap el-scrollbar__wrap el-scrollbar__wrap--hidden-default"><ul class="el-scrollbar__view el-select-dropdown__list" style=""><!--v-if--><li class="el-select-dropdown__item selected"><span>10条/页</span></li><li class="el-select-dropdown__item"><span>20条/页</span></li><li class="el-select-dropdown__item"><span>50条/页</span></li><li class="el-select-dropdown__item"><span>100条/页</span></li></ul></div><div class="el-scrollbar__bar is-horizontal" style="display: none;"><div class="el-scrollbar__thumb" style="transform: translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical" style="display: none;"><div class="el-scrollbar__thumb" style="transform: translateY(0%);"></div></div></div><!--v-if--></div><span class="el-popper__arrow" data-popper-arrow="" style="position: absolute; left: 0px;"></span></div></div></body></html>