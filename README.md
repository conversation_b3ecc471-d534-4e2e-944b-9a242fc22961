# 考研搜网站爬虫

这是一个用于爬取考研搜网站(kaoyansou.cn)学校专业录取数据的Python爬虫工具。

## 功能特点

- 🎯 **精准提取**: 自动提取学校名、专业名、专业编号、年份、初试成绩等关键信息
- 📊 **多年份数据**: 支持爬取多个年份的历史数据
- 📈 **数据统计**: 自动计算最低分、最高分、平均分和录取人数
- 💾 **多格式保存**: 支持CSV和JSON格式保存数据
- 🤖 **智能驱动管理**: 自动下载和管理Chrome驱动程序
- 🔄 **容错处理**: 完善的异常处理和重试机制

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基础版本
```bash
python kaoyansou_crawler.py
```

### 增强版本（推荐）
```bash
python kaoyansou_crawler_enhanced.py
```

## 输出数据

爬虫会生成以下文件：

1. **详细数据文件** (`*_details.csv`): 包含每个学生的详细录取信息
   - 学校名称
   - 专业名称和编号
   - 年份
   - 各科成绩（政治、外语、业务课一、业务课二）
   - 学生姓名（脱敏）
   - 第一志愿学校
   - 总分
   - 初试成绩
   - 复试成绩
   - 专项计划

2. **汇总数据文件** (`*_summary.csv`): 包含每年的统计信息
   - 最低初试成绩
   - 最高初试成绩
   - 平均初试成绩
   - 录取人数

3. **JSON文件**: 包含所有数据的完整JSON格式

## 数据示例

### 详细数据示例
```csv
school_name,major_name,major_code,year,politics_score,foreign_lang_score,subject1_score,subject2_score,student_name,first_choice_school,total_score,initial_test_score,retest_score,special_plan
清华大学,软件工程,083500,2024,69,75,116,120,杨**,清华大学,778.35,380,398.35,
清华大学,软件工程,083500,2024,75,75,108,111,穆**,清华大学,763,369,394,
```

### 汇总数据示例
```csv
school_name,major_name,major_code,year,min_initial_test_score,max_initial_test_score,avg_initial_test_score,total_students
清华大学,软件工程,083500,2024,369,380,374.5,2
```

## 配置选项

### 修改目标URL
在脚本中修改 `url` 变量：
```python
url = "你的目标网页URL"
```

### 浏览器模式
- `headless=True`: 无头模式（后台运行，不显示浏览器窗口）
- `headless=False`: 有头模式（显示浏览器操作过程，便于调试）

## 注意事项

1. **Chrome浏览器**: 需要安装Chrome浏览器，脚本会自动下载对应的驱动程序
2. **网络连接**: 确保网络连接稳定，避免数据加载失败
3. **访问频率**: 脚本已内置延时机制，避免过于频繁的请求
4. **数据准确性**: 建议对比官方数据验证爬取结果的准确性
5. **法律合规**: 请遵守网站的robots.txt和使用条款

## 故障排除

### 常见问题

1. **Chrome驱动问题**
   - 脚本会自动下载匹配的Chrome驱动
   - 如果失败，请检查Chrome浏览器版本

2. **页面加载超时**
   - 检查网络连接
   - 增加等待时间（修改 `WebDriverWait` 的超时时间）

3. **数据提取失败**
   - 网页结构可能发生变化
   - 检查CSS选择器是否仍然有效

4. **年份选择失败**
   - 某些年份可能没有数据
   - 脚本会自动跳过无效年份

### 调试模式
设置 `headless=False` 可以看到浏览器的实际操作过程，便于调试问题。

## 扩展功能

可以根据需要扩展以下功能：
- 支持批量爬取多个专业
- 添加数据可视化功能
- 集成数据库存储
- 添加邮件通知功能
- 支持定时任务

## 免责声明

本工具仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。使用者需要对使用本工具产生的任何后果承担责任。

## 许可证

MIT License
