#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版爬虫 - 用于诊断问题
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException


def setup_debug_driver():
    """设置调试用的Chrome驱动"""
    chrome_options = Options()
    # 不使用无头模式，方便观察
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_argument('--disable-web-security')
    chrome_options.add_argument('--allow-running-insecure-content')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 尝试使用你的驱动路径
    driver_path = r"C:\Users\<USER>\Downloads\chromedriver-win64\chromedriver-win64\chromedriver.exe"
    
    if os.path.exists(driver_path):
        print(f"使用驱动路径: {driver_path}")
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)
    else:
        print("驱动路径不存在，尝试默认路径")
        driver = webdriver.Chrome(options=chrome_options)
    
    # 隐藏webdriver特征
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver


def debug_page_loading(driver, url):
    """调试页面加载"""
    print(f"正在访问: {url}")
    driver.get(url)
    
    print("等待页面加载...")
    time.sleep(10)
    
    print(f"当前URL: {driver.current_url}")
    print(f"页面标题: {driver.title}")
    
    # 检查页面是否正确加载
    if "kaoyansou" not in driver.current_url:
        print("❌ 页面可能被重定向了")
        return False
    
    # 保存页面截图
    try:
        driver.save_screenshot("debug_page.png")
        print("✓ 页面截图已保存为 debug_page.png")
    except:
        pass
    
    # 检查页面源码
    page_source = driver.page_source
    print(f"页面源码长度: {len(page_source)}")
    
    # 检查是否包含关键元素
    key_elements = [
        "清华大学",
        "软件工程", 
        "083500",
        "sch-name",
        "el-select",
        "录取名单"
    ]
    
    found_elements = []
    for element in key_elements:
        if element in page_source:
            found_elements.append(element)
            print(f"✓ 找到关键词: {element}")
        else:
            print(f"❌ 未找到关键词: {element}")
    
    return len(found_elements) > 3


def debug_element_finding(driver):
    """调试元素查找"""
    print("\n=== 调试元素查找 ===")
    
    # 尝试查找学校名相关元素
    school_selectors = [
        ".sch-name",
        ".sch-name span",
        ".sch-name span:first-child",
        ".right .sch-name",
        "[class*='sch-name']"
    ]
    
    print("查找学校名元素:")
    for selector in school_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"✓ {selector}: 找到 {len(elements)} 个元素")
                for i, elem in enumerate(elements[:3]):  # 只显示前3个
                    try:
                        text = elem.text.strip()
                        if text:
                            print(f"  [{i}] 文本: {text}")
                    except:
                        print(f"  [{i}] 无法获取文本")
            else:
                print(f"❌ {selector}: 未找到元素")
        except Exception as e:
            print(f"❌ {selector}: 查找失败 - {e}")
    
    # 尝试查找年份选择器
    print("\n查找年份选择器:")
    year_selectors = [
        ".el-select",
        ".el-select input",
        ".el-select .el-input__inner",
        "input[placeholder*='年']",
        "[class*='select']"
    ]
    
    for selector in year_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"✓ {selector}: 找到 {len(elements)} 个元素")
                for i, elem in enumerate(elements[:2]):
                    try:
                        placeholder = elem.get_attribute('placeholder')
                        value = elem.get_attribute('value')
                        print(f"  [{i}] placeholder: {placeholder}, value: {value}")
                    except:
                        print(f"  [{i}] 无法获取属性")
            else:
                print(f"❌ {selector}: 未找到元素")
        except Exception as e:
            print(f"❌ {selector}: 查找失败 - {e}")


def debug_table_elements(driver):
    """调试表格元素"""
    print("\n=== 调试表格元素 ===")
    
    table_selectors = [
        ".el-table",
        ".el-table__body",
        ".el-table__body tr",
        "table",
        "[class*='table']"
    ]
    
    for selector in table_selectors:
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            if elements:
                print(f"✓ {selector}: 找到 {len(elements)} 个元素")
                if "tr" in selector and elements:
                    # 检查表格行的内容
                    for i, row in enumerate(elements[:3]):
                        try:
                            cells = row.find_elements(By.CSS_SELECTOR, "td")
                            if cells:
                                cell_texts = [cell.text.strip() for cell in cells[:5]]
                                print(f"  行[{i}]: {cell_texts}")
                        except:
                            print(f"  行[{i}]: 无法获取单元格内容")
            else:
                print(f"❌ {selector}: 未找到元素")
        except Exception as e:
            print(f"❌ {selector}: 查找失败 - {e}")


def main():
    """主调试函数"""
    url = "https://www.kaoyansou.cn/f/sc/index.html#/school/10003/detail?majorOutRelationId=578447"
    
    print("=== 开始调试 ===")
    
    try:
        driver = setup_debug_driver()
        print("✓ Chrome驱动设置成功")
        
        # 调试页面加载
        if debug_page_loading(driver, url):
            print("✓ 页面加载成功")
            
            # 调试元素查找
            debug_element_finding(driver)
            
            # 调试表格元素
            debug_table_elements(driver)
            
        else:
            print("❌ 页面加载失败")
        
        print("\n调试完成，按回车键关闭浏览器...")
        input()
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            driver.quit()
        except:
            pass


if __name__ == "__main__":
    main()
