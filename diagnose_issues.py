#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题诊断脚本
帮助诊断Chrome驱动和网页访问问题
"""

import os
import subprocess
import sys
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service


def check_chrome_version():
    """检查Chrome浏览器版本"""
    try:
        # Windows系统检查Chrome版本
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ]
        
        for path in chrome_paths:
            if os.path.exists(path):
                result = subprocess.run([path, "--version"], capture_output=True, text=True)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    print(f"✓ Chrome版本: {version}")
                    return version
        
        print("❌ 未找到Chrome浏览器")
        return None
    except Exception as e:
        print(f"❌ 检查Chrome版本失败: {e}")
        return None


def check_chromedriver():
    """检查ChromeDriver"""
    driver_path = r"C:\Users\<USER>\Downloads\chromedriver-win64\chromedriver-win64\chromedriver.exe"
    
    if os.path.exists(driver_path):
        print(f"✓ 找到ChromeDriver: {driver_path}")
        try:
            result = subprocess.run([driver_path, "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"✓ ChromeDriver版本: {version}")
                return True
            else:
                print("❌ ChromeDriver无法执行")
                return False
        except Exception as e:
            print(f"❌ 检查ChromeDriver版本失败: {e}")
            return False
    else:
        print(f"❌ 未找到ChromeDriver: {driver_path}")
        return False


def test_basic_selenium():
    """测试基本Selenium功能"""
    try:
        print("测试基本Selenium功能...")
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        driver_path = r"C:\Users\<USER>\Downloads\chromedriver-win64\chromedriver-win64\chromedriver.exe"
        
        if os.path.exists(driver_path):
            service = Service(driver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)
        
        # 测试访问简单网页
        driver.get("https://www.baidu.com")
        title = driver.title
        print(f"✓ 成功访问百度，页面标题: {title}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ Selenium基本功能测试失败: {e}")
        return False


def test_target_website():
    """测试目标网站访问"""
    try:
        print("测试目标网站访问...")
        
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        driver_path = r"C:\Users\<USER>\Downloads\chromedriver-win64\chromedriver-win64\chromedriver.exe"
        
        if os.path.exists(driver_path):
            service = Service(driver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)
        
        url = "https://www.kaoyansou.cn/f/sc/index.html#/school/10003/detail?majorOutRelationId=578447"
        print(f"正在访问: {url}")
        
        driver.get(url)
        
        # 等待页面加载
        import time
        time.sleep(10)
        
        current_url = driver.current_url
        title = driver.title
        page_source_length = len(driver.page_source)
        
        print(f"✓ 当前URL: {current_url}")
        print(f"✓ 页面标题: {title}")
        print(f"✓ 页面源码长度: {page_source_length}")
        
        # 检查关键内容
        page_source = driver.page_source
        key_words = ["清华大学", "软件工程", "083500", "录取名单"]
        found_words = []
        
        for word in key_words:
            if word in page_source:
                found_words.append(word)
                print(f"✓ 找到关键词: {word}")
            else:
                print(f"❌ 未找到关键词: {word}")
        
        # 保存页面截图和源码
        try:
            driver.save_screenshot("diagnosis_screenshot.png")
            print("✓ 页面截图已保存为 diagnosis_screenshot.png")
        except:
            pass
        
        try:
            with open("diagnosis_page_source.html", "w", encoding="utf-8") as f:
                f.write(page_source)
            print("✓ 页面源码已保存为 diagnosis_page_source.html")
        except:
            pass
        
        driver.quit()
        
        return len(found_words) > 0
        
    except Exception as e:
        print(f"❌ 目标网站访问测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_python_packages():
    """检查Python包"""
    required_packages = [
        'selenium',
        'webdriver-manager',
        'pandas',
        'requests'
    ]
    
    print("检查Python包...")
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n需要安装的包: {', '.join(missing_packages)}")
        print("运行命令: pip install " + " ".join(missing_packages))
        return False
    
    return True


def main():
    """主诊断函数"""
    print("=== 开始问题诊断 ===\n")
    
    # 1. 检查Python包
    print("1. 检查Python包...")
    packages_ok = check_python_packages()
    print()
    
    # 2. 检查Chrome版本
    print("2. 检查Chrome浏览器...")
    chrome_version = check_chrome_version()
    print()
    
    # 3. 检查ChromeDriver
    print("3. 检查ChromeDriver...")
    driver_ok = check_chromedriver()
    print()
    
    # 4. 测试基本Selenium功能
    print("4. 测试基本Selenium功能...")
    selenium_ok = test_basic_selenium()
    print()
    
    # 5. 测试目标网站访问
    print("5. 测试目标网站访问...")
    website_ok = test_target_website()
    print()
    
    # 总结
    print("=== 诊断结果 ===")
    print(f"Python包: {'✓' if packages_ok else '❌'}")
    print(f"Chrome浏览器: {'✓' if chrome_version else '❌'}")
    print(f"ChromeDriver: {'✓' if driver_ok else '❌'}")
    print(f"Selenium基本功能: {'✓' if selenium_ok else '❌'}")
    print(f"目标网站访问: {'✓' if website_ok else '❌'}")
    
    if all([packages_ok, chrome_version, driver_ok, selenium_ok, website_ok]):
        print("\n🎉 所有检查通过！爬虫应该可以正常工作。")
        print("如果仍有问题，请检查网络连接或网站是否有反爬虫措施。")
    else:
        print("\n❌ 发现问题，请根据上述检查结果进行修复。")
        
        if not packages_ok:
            print("- 安装缺失的Python包")
        if not chrome_version:
            print("- 安装Chrome浏览器")
        if not driver_ok:
            print("- 下载正确版本的ChromeDriver")
        if not selenium_ok:
            print("- 检查Selenium配置")
        if not website_ok:
            print("- 检查网络连接和目标网站状态")


if __name__ == "__main__":
    main()
