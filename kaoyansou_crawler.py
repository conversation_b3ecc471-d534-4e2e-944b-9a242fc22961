#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考研搜网站爬虫
爬取学校、专业、年份、初试成绩等信息
"""

import time
import json
import csv
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re


class KaoYanSouCrawler:
    def __init__(self, headless=False):
        """初始化爬虫"""
        self.setup_driver(headless)
        self.data = []
        
    def setup_driver(self, headless=False):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        self.driver = webdriver.Chrome(options=chrome_options)
        self.wait = WebDriverWait(self.driver, 10)
        
    def extract_basic_info(self):
        """提取基本信息：学校名、专业名、专业编号"""
        try:
            # 提取学校名
            school_name = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".sch-name span:first-child"))
            ).text.strip()
            
            # 提取专业名和专业编号
            major_element = self.driver.find_element(By.CSS_SELECTOR, ".sch-name .major:first-of-type")
            major_text = major_element.text.strip()
            
            # 使用正则表达式提取专业名和编号
            match = re.match(r'(.+?)\((\d+)\)', major_text)
            if match:
                major_name = match.group(1).strip()
                major_code = match.group(2).strip()
            else:
                major_name = major_text
                major_code = ""
            
            return {
                'school_name': school_name,
                'major_name': major_name,
                'major_code': major_code
            }
        except Exception as e:
            print(f"提取基本信息失败: {e}")
            return {
                'school_name': '',
                'major_name': '',
                'major_code': ''
            }
    
    def get_available_years(self):
        """获取可用的年份列表"""
        try:
            # 点击年份选择下拉框
            year_select = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".el-select .el-input__inner"))
            )
            year_select.click()
            time.sleep(2)
            
            # 获取所有年份选项
            year_options = self.driver.find_elements(By.CSS_SELECTOR, ".el-select-dropdown__item")
            years = []
            for option in year_options:
                year_text = option.text.strip()
                if year_text and year_text.isdigit():
                    years.append(year_text)
            
            # 点击空白处关闭下拉框
            self.driver.find_element(By.TAG_NAME, "body").click()
            time.sleep(1)
            
            return years
        except Exception as e:
            print(f"获取年份列表失败: {e}")
            return ['2024']  # 默认返回2024年
    
    def select_year(self, year):
        """选择指定年份"""
        try:
            # 点击年份选择下拉框
            year_select = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".el-select .el-input__inner"))
            )
            year_select.click()
            time.sleep(2)
            
            # 查找并点击指定年份
            year_options = self.driver.find_elements(By.CSS_SELECTOR, ".el-select-dropdown__item")
            for option in year_options:
                if option.text.strip() == str(year):
                    option.click()
                    time.sleep(3)  # 等待数据加载
                    return True
            
            print(f"未找到年份 {year}")
            return False
        except Exception as e:
            print(f"选择年份 {year} 失败: {e}")
            return False
    
    def extract_table_data(self, basic_info, year):
        """提取表格数据"""
        try:
            # 等待表格加载
            table_rows = self.wait.until(
                EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".el-table__body tbody tr"))
            )
            
            year_data = []
            min_score = float('inf')
            
            for row in table_rows:
                try:
                    cells = row.find_elements(By.CSS_SELECTOR, "td .cell")
                    if len(cells) >= 9:  # 确保有足够的列
                        # 提取初试成绩（第9列，索引为8）
                        score_text = cells[8].text.strip()
                        if score_text and score_text.isdigit():
                            score = int(score_text)
                            min_score = min(min_score, score)
                            
                            row_data = {
                                'school_name': basic_info['school_name'],
                                'major_name': basic_info['major_name'],
                                'major_code': basic_info['major_code'],
                                'year': year,
                                'politics_score': cells[1].text.strip(),
                                'foreign_lang_score': cells[2].text.strip(),
                                'subject1_score': cells[3].text.strip(),
                                'subject2_score': cells[4].text.strip(),
                                'student_name': cells[5].text.strip(),
                                'first_choice_school': cells[6].text.strip(),
                                'total_score': cells[7].text.strip(),
                                'initial_test_score': score_text,
                                'retest_score': cells[9].text.strip() if len(cells) > 9 else '',
                                'special_plan': cells[10].text.strip() if len(cells) > 10 else ''
                            }
                            year_data.append(row_data)
                except Exception as e:
                    print(f"处理行数据失败: {e}")
                    continue
            
            # 添加最低分信息
            if min_score != float('inf'):
                summary_data = {
                    'school_name': basic_info['school_name'],
                    'major_name': basic_info['major_name'],
                    'major_code': basic_info['major_code'],
                    'year': year,
                    'min_initial_test_score': min_score,
                    'data_type': 'summary'
                }
                year_data.append(summary_data)
            
            return year_data
        except Exception as e:
            print(f"提取表格数据失败: {e}")
            return []
    
    def crawl_data(self, url):
        """爬取数据主函数"""
        try:
            print(f"正在访问: {url}")
            self.driver.get(url)
            time.sleep(5)
            
            # 提取基本信息
            basic_info = self.extract_basic_info()
            print(f"学校: {basic_info['school_name']}")
            print(f"专业: {basic_info['major_name']} ({basic_info['major_code']})")
            
            # 获取可用年份
            years = self.get_available_years()
            print(f"可用年份: {years}")
            
            # 爬取每个年份的数据
            for year in years:
                print(f"\n正在爬取 {year} 年数据...")
                if self.select_year(year):
                    year_data = self.extract_table_data(basic_info, year)
                    self.data.extend(year_data)
                    print(f"{year} 年数据爬取完成，共 {len(year_data)} 条记录")
                else:
                    print(f"{year} 年数据爬取失败")
                
                time.sleep(2)  # 避免请求过快
            
            print(f"\n总共爬取 {len(self.data)} 条数据")
            return self.data
            
        except Exception as e:
            print(f"爬取数据失败: {e}")
            return []
    
    def save_to_csv(self, filename='kaoyansou_data.csv'):
        """保存数据到CSV文件"""
        if not self.data:
            print("没有数据可保存")
            return
        
        # 获取所有字段名
        fieldnames = set()
        for item in self.data:
            fieldnames.update(item.keys())
        fieldnames = sorted(list(fieldnames))
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.data)
        
        print(f"数据已保存到 {filename}")
    
    def save_to_json(self, filename='kaoyansou_data.json'):
        """保存数据到JSON文件"""
        if not self.data:
            print("没有数据可保存")
            return
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.data, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"数据已保存到 {filename}")
    
    def close(self):
        """关闭浏览器"""
        if hasattr(self, 'driver'):
            self.driver.quit()


def main():
    """主函数"""
    url = "https://www.kaoyansou.cn/f/sc/index.html#/school/10003/detail?majorOutRelationId=578447"
    
    crawler = KaoYanSouCrawler(headless=False)  # 设置为True可以无头模式运行
    
    try:
        # 爬取数据
        data = crawler.crawl_data(url)
        
        if data:
            # 保存数据
            crawler.save_to_csv()
            crawler.save_to_json()
            
            # 打印摘要信息
            print("\n=== 数据摘要 ===")
            summary_data = [item for item in data if item.get('data_type') == 'summary']
            for summary in summary_data:
                print(f"{summary['year']}年 {summary['school_name']} {summary['major_name']} 初试最低分: {summary['min_initial_test_score']}")
        
    except Exception as e:
        print(f"程序执行失败: {e}")
    finally:
        crawler.close()


if __name__ == "__main__":
    main()
