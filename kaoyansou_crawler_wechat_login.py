#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考研搜网站爬虫 - 微信登录版
支持手动输入微信动态码进行登录
使用webdriver-manager自动管理Chrome驱动
支持多年份数据爬取和数据分析
"""

import time
import json
import csv
import pandas as pd
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re
import os
from datetime import datetime


class KaoYanSouCrawlerWeChatLogin:
    def __init__(self, headless=True):
        """初始化爬虫"""
        self.setup_driver(headless)
        self.data = []
        self.basic_info = {}
        self.logged_in = False

    def setup_driver(self, headless=True):
        """设置Chrome浏览器驱动，支持手动指定驱动路径"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        # 尝试使用webdriver-manager自动下载和管理Chrome驱动
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            print("成功使用webdriver-manager自动下载Chrome驱动")
        except Exception as e:
            print(f"自动下载Chrome驱动失败: {e}")
            print("尝试使用手动指定的驱动路径...")

            # 手动指定Chrome驱动路径
            # 请根据实际情况修改此路径
            driver_path = r"C:\Users\<USER>\Downloads\chromedriver-win64\chromedriver-win64\chromedriver.exe"

            # 如果上述路径不存在，尝试其他可能的路径
            if not os.path.exists(driver_path):
                driver_path = "C:\\Users\\<USER>\\Downloads\\chromedriver.exe"
                print(f"尝试使用备用路径: {driver_path}")

            # 检查驱动文件是否存在
            if os.path.exists(driver_path):
                try:
                    service = Service(executable_path=driver_path)
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    print(f"成功使用手动指定的驱动: {driver_path}")
                except Exception as e2:
                    print(f"使用手动驱动失败: {e2}")
                    raise Exception("无法初始化Chrome驱动，请确保已安装Chrome浏览器并下载了对应版本的chromedriver")
            else:
                raise Exception(f"找不到Chrome驱动文件: {driver_path}。请手动下载Chrome驱动并指定正确路径。")

        # 执行脚本来隐藏webdriver特征
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        self.wait = WebDriverWait(self.driver, 15)

    def login_with_wechat_code(self, code=None):
        """使用微信动态码登录"""
        try:
            # 访问登录页面
            self.driver.get("https://www.kaoyansou.cn")
            time.sleep(3)

            # 查找登录按钮
            login_button = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".login .el-button--primary"))
            )
            login_button.click()
            time.sleep(3)

            # 如果没有提供动态码，提示用户输入
            if code is None:
                print("请在微信上获取动态码，然后在此输入:")
                code = input("动态码: ")

            # 查找动态码输入框并输入
            code_input = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".el-dialog__body input[type='text']"))
            )
            code_input.clear()
            code_input.send_keys(code)

            # 查找并点击确认按钮
            confirm_button = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".el-dialog__footer .el-button--primary"))
            )
            confirm_button.click()

            print("请等待登录完成，这可能需要一些时间...")

            # 等待登录完成 - 增加到30秒
            time.sleep(30)

            # 检查是否登录成功
            login_success = False

            # 方法1：检查页面URL是否变化
            current_url = self.driver.current_url
            if "login" not in current_url:
                login_success = True

            # 方法2：查找用户名元素，如果存在则表示登录成功
            if not login_success:
                try:
                    user_element = self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".login .el-button--primary span"))
                    )
                    if "退出" in user_element.text or "个人中心" in user_element.text:
                        login_success = True
                except TimeoutException:
                    pass

            # 方法3：检查页面源码中是否包含特定登录成功标识
            if not login_success:
                page_source = self.driver.page_source
                if "退出" in page_source or "个人中心" in page_source:
                    login_success = True

            # 根据检查结果返回状态
            if login_success:
                self.logged_in = True
                print("[登录成功]")
                return True
            else:
                print("[登录失败]，请检查动态码是否正确")
                return False

        except Exception as e:
            print(f"登录过程中出错: {e}")
            return False

    def extract_basic_info(self):
        """提取基本信息：学校名、专业名、专业编号"""
        try:
            # 等待页面加载
            time.sleep(3)

            # 提取学校名
            school_element = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".sch-name span:first-child"))
            )
            school_name = school_element.text.strip()

            # 提取专业名和专业编号
            major_elements = self.driver.find_elements(By.CSS_SELECTOR, ".sch-name .major")
            major_text = ""
            for element in major_elements:
                text = element.text.strip()
                if '(' in text and ')' in text:
                    major_text = text
                    break

            # 使用正则表达式提取专业名和编号
            major_name = ""
            major_code = ""
            if major_text:
                match = re.match(r'(.+?)\((\d+)\)', major_text)
                if match:
                    major_name = match.group(1).strip()
                    major_code = match.group(2).strip()
                else:
                    major_name = major_text

            self.basic_info = {
                'school_name': school_name,
                'major_name': major_name,
                'major_code': major_code
            }

            return self.basic_info
        except Exception as e:
            print(f"提取基本信息失败: {e}")
            return {
                'school_name': '',
                'major_name': '',
                'major_code': ''
            }

    def get_available_years(self):
        """获取可用的年份列表"""
        try:
            # 等待年份选择器加载
            time.sleep(2)

            # 点击年份选择下拉框
            year_select = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".el-select .el-input__inner"))
            )
            self.driver.execute_script("arguments[0].click();", year_select)
            time.sleep(3)

            # 获取所有年份选项
            year_options = self.wait.until(
                EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".el-select-dropdown__item"))
            )

            years = []
            for option in year_options:
                year_text = option.text.strip()
                if year_text and year_text.isdigit() and len(year_text) == 4:
                    years.append(year_text)

            # 点击空白处关闭下拉框
            self.driver.find_element(By.TAG_NAME, "body").click()
            time.sleep(2)

            # 如果没有找到年份，返回默认年份
            if not years:
                years = ['2024']

            return sorted(years, reverse=True)  # 按年份降序排列
        except Exception as e:
            print(f"获取年份列表失败: {e}")
            return ['2024']  # 默认返回2024年

    def select_year(self, year):
        """选择指定年份"""
        try:
            print(f"正在选择年份: {year}")

            # 点击年份选择下拉框
            year_select = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".el-select .el-input__inner"))
            )
            self.driver.execute_script("arguments[0].click();", year_select)
            time.sleep(2)

            # 查找并点击指定年份
            year_options = self.wait.until(
                EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".el-select-dropdown__item"))
            )

            for option in year_options:
                if option.text.strip() == str(year):
                    self.driver.execute_script("arguments[0].click();", option)
                    time.sleep(4)  # 等待数据加载
                    return True

            print(f"未找到年份 {year}")
            return False
        except Exception as e:
            print(f"选择年份 {year} 失败: {e}")
            return False

    def extract_table_data(self, year):
        """提取表格数据"""
        try:
            # 等待表格加载
            time.sleep(3)

            # 检查是否有数据
            try:
                table_rows = self.wait.until(
                    EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".el-table__body tbody tr"))
                )
            except TimeoutException:
                print(f"{year}年暂无数据")
                return []

            year_data = []
            scores = []

            for i, row in enumerate(table_rows):
                try:
                    cells = row.find_elements(By.CSS_SELECTOR, "td .cell")
                    if len(cells) >= 9:  # 确保有足够的列
                        # 提取初试成绩（第9列，索引为8）
                        score_text = cells[8].text.strip()
                        if score_text and score_text.replace('.', '').isdigit():
                            score = float(score_text)
                            scores.append(score)

                            row_data = {
                                'school_name': self.basic_info['school_name'],
                                'major_name': self.basic_info['major_name'],
                                'major_code': self.basic_info['major_code'],
                                'year': year,
                                'politics_score': cells[1].text.strip(),
                                'foreign_lang_score': cells[2].text.strip(),
                                'subject1_score': cells[3].text.strip(),
                                'subject2_score': cells[4].text.strip(),
                                'student_name': cells[5].text.strip(),
                                'first_choice_school': cells[6].text.strip(),
                                'total_score': cells[7].text.strip(),
                                'initial_test_score': score,
                                'retest_score': cells[9].text.strip() if len(cells) > 9 else '',
                                'special_plan': cells[10].text.strip() if len(cells) > 10 else '',
                                'data_type': 'detail'
                            }
                            year_data.append(row_data)
                except Exception as e:
                    print(f"处理第{i+1}行数据失败: {e}")
                    continue

            # 添加统计信息
            if scores:
                min_score = min(scores)
                max_score = max(scores)
                avg_score = sum(scores) / len(scores)

                summary_data = {
                    'school_name': self.basic_info['school_name'],
                    'major_name': self.basic_info['major_name'],
                    'major_code': self.basic_info['major_code'],
                    'year': year,
                    'min_initial_test_score': min_score,
                    'max_initial_test_score': max_score,
                    'avg_initial_test_score': round(avg_score, 2),
                    'total_students': len(scores),
                    'data_type': 'summary'
                }
                year_data.append(summary_data)

                print(f"{year}年数据: 最低分{min_score}, 最高分{max_score}, 平均分{avg_score:.2f}, 录取人数{len(scores)}")

            return year_data
        except Exception as e:
            print(f"提取{year}年表格数据失败: {e}")
            return []

    def crawl_data(self, url, wechat_code=None):
        """爬取数据主函数"""
        try:
            # 首先登录
            if not self.logged_in:
                print("正在进行微信登录...")
                if not self.login_with_wechat_code(wechat_code):
                    print("登录失败，无法继续爬取数据")
                    return []

            print(f"正在访问: {url}")
            self.driver.get(url)
            time.sleep(5)

            # 提取基本信息
            basic_info = self.extract_basic_info()
            print(f"学校: {basic_info['school_name']}")
            print(f"专业: {basic_info['major_name']} ({basic_info['major_code']})")

            # 获取可用年份
            years = self.get_available_years()
            print(f"可用年份: {years}")

            # 爬取每个年份的数据
            for year in years:
                print(f"正在爬取 {year} 年数据...")
                if self.select_year(year):
                    year_data = self.extract_table_data(year)
                    self.data.extend(year_data)
                    detail_count = len([d for d in year_data if d.get('data_type') == 'detail'])
                    print(f"{year} 年数据爬取完成，共 {detail_count} 条详细记录")
                else:
                    print(f"{year} 年数据爬取失败")

                time.sleep(2)  # 避免请求过快

            total_details = len([d for d in self.data if d.get('data_type') == 'detail'])
            total_summaries = len([d for d in self.data if d.get('data_type') == 'summary'])
            print(f"总共爬取 {total_details} 条详细记录，{total_summaries} 条汇总记录")
            return self.data

        except Exception as e:
            print(f"爬取数据失败: {e}")
            return []

    def save_to_csv(self, filename=None):
        """保存数据到CSV文件"""
        if not self.data:
            print("没有数据可保存")
            return

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'kaoyansou_data_{timestamp}.csv'

        # 分别保存详细数据和汇总数据
        detail_data = [d for d in self.data if d.get('data_type') == 'detail']
        summary_data = [d for d in self.data if d.get('data_type') == 'summary']

        if detail_data:
            detail_filename = filename.replace('.csv', '_details.csv')
            df_detail = pd.DataFrame(detail_data)
            df_detail.to_csv(detail_filename, index=False, encoding='utf-8-sig')
            print(f"详细数据已保存到 {detail_filename}")

        if summary_data:
            summary_filename = filename.replace('.csv', '_summary.csv')
            df_summary = pd.DataFrame(summary_data)
            df_summary.to_csv(summary_filename, index=False, encoding='utf-8-sig')
            print(f"汇总数据已保存到 {summary_filename}")

    def save_to_json(self, filename=None):
        """保存数据到JSON文件"""
        if not self.data:
            print("没有数据可保存")
            return

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'kaoyansou_data_{timestamp}.json'

        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.data, jsonfile, ensure_ascii=False, indent=2)

        print(f"数据已保存到 {filename}")

    def print_summary(self):
        """打印数据摘要"""
        if not self.data:
            print("没有数据可显示")
            return

        print("=== 数据摘要 ===")
        summary_data = [item for item in self.data if item.get('data_type') == 'summary']

        if summary_data:
            for summary in sorted(summary_data, key=lambda x: x['year'], reverse=True):
                print(f"{summary['year']}年 {summary['school_name']} {summary['major_name']}:")
                print(f"  初试最低分: {summary['min_initial_test_score']}")
                print(f"  初试最高分: {summary['max_initial_test_score']}")
                print(f"  初试平均分: {summary['avg_initial_test_score']}")
                print(f"  录取人数: {summary['total_students']}")
                print()

    def close(self):
        """关闭浏览器"""
        if hasattr(self, 'driver'):
            self.driver.quit()


def main():
    """主函数"""
    url = "https://www.kaoyansou.cn/f/sc/index.html#/school/10003/detail?majorOutRelationId=578447"

    # 创建爬虫实例，headless=False可以看到浏览器操作过程
    crawler = KaoYanSouCrawlerWeChatLogin(headless=False)

    try:
        # 爬取数据，会提示输入微信动态码
        data = crawler.crawl_data(url)

        if data:
            # 保存数据
            crawler.save_to_csv()
            crawler.save_to_json()

            # 打印摘要信息
            crawler.print_summary()
        else:
            print("未能获取到任何数据")

    except Exception as e:
        print(f"程序执行失败: {e}")
    finally:
        crawler.close()


if __name__ == "__main__":
    main()
