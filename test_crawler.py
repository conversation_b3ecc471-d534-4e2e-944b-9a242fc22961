#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
爬虫测试脚本
用于测试爬虫的基本功能
"""

import sys
import os
from kaoyansou_crawler_enhanced import KaoYanSouCrawlerEnhanced


def test_basic_functionality():
    """测试基本功能"""
    print("=== 开始测试爬虫基本功能 ===")
    
    # 测试URL
    test_url = "https://www.kaoyansou.cn/f/sc/index.html#/school/10003/detail?majorOutRelationId=578447"
    
    # 创建爬虫实例（使用有头模式便于观察）
    crawler = KaoYanSouCrawlerEnhanced(headless=False)
    
    try:
        print("1. 测试页面访问...")
        crawler.driver.get(test_url)
        print("✓ 页面访问成功")
        
        print("\n2. 测试基本信息提取...")
        basic_info = crawler.extract_basic_info()
        if basic_info['school_name']:
            print(f"✓ 学校名: {basic_info['school_name']}")
            print(f"✓ 专业名: {basic_info['major_name']}")
            print(f"✓ 专业编号: {basic_info['major_code']}")
        else:
            print("✗ 基本信息提取失败")
            return False
        
        print("\n3. 测试年份获取...")
        years = crawler.get_available_years()
        if years:
            print(f"✓ 可用年份: {years}")
        else:
            print("✗ 年份获取失败")
            return False
        
        print("\n4. 测试数据提取（仅测试第一个年份）...")
        if crawler.select_year(years[0]):
            data = crawler.extract_table_data(years[0])
            if data:
                detail_count = len([d for d in data if d.get('data_type') == 'detail'])
                summary_count = len([d for d in data if d.get('data_type') == 'summary'])
                print(f"✓ 数据提取成功: {detail_count} 条详细记录, {summary_count} 条汇总记录")
                
                # 显示第一条详细记录
                if detail_count > 0:
                    first_detail = next(d for d in data if d.get('data_type') == 'detail')
                    print(f"✓ 示例数据: 初试成绩 {first_detail['initial_test_score']}")
            else:
                print("✗ 数据提取失败")
                return False
        else:
            print("✗ 年份选择失败")
            return False
        
        print("\n=== 基本功能测试通过 ===")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False
    finally:
        crawler.close()


def test_data_saving():
    """测试数据保存功能"""
    print("\n=== 测试数据保存功能 ===")
    
    # 创建测试数据
    test_data = [
        {
            'school_name': '清华大学',
            'major_name': '软件工程',
            'major_code': '083500',
            'year': '2024',
            'initial_test_score': 380,
            'data_type': 'detail'
        },
        {
            'school_name': '清华大学',
            'major_name': '软件工程',
            'major_code': '083500',
            'year': '2024',
            'min_initial_test_score': 369,
            'max_initial_test_score': 380,
            'avg_initial_test_score': 374.5,
            'total_students': 2,
            'data_type': 'summary'
        }
    ]
    
    crawler = KaoYanSouCrawlerEnhanced()
    crawler.data = test_data
    crawler.basic_info = {
        'school_name': '清华大学',
        'major_name': '软件工程',
        'major_code': '083500'
    }
    
    try:
        print("1. 测试CSV保存...")
        crawler.save_to_csv('test_output.csv')
        
        # 检查文件是否生成
        if os.path.exists('test_output_details.csv') and os.path.exists('test_output_summary.csv'):
            print("✓ CSV文件保存成功")
        else:
            print("✗ CSV文件保存失败")
            return False
        
        print("2. 测试JSON保存...")
        crawler.save_to_json('test_output.json')
        
        if os.path.exists('test_output.json'):
            print("✓ JSON文件保存成功")
        else:
            print("✗ JSON文件保存失败")
            return False
        
        print("3. 测试数据摘要...")
        crawler.print_summary()
        
        print("\n=== 数据保存功能测试通过 ===")
        return True
        
    except Exception as e:
        print(f"✗ 数据保存测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        for filename in ['test_output_details.csv', 'test_output_summary.csv', 'test_output.json']:
            if os.path.exists(filename):
                os.remove(filename)
        crawler.close()


def main():
    """主测试函数"""
    print("考研搜爬虫测试程序")
    print("=" * 50)
    
    # 检查依赖
    try:
        from selenium import webdriver
        from webdriver_manager.chrome import ChromeDriverManager
        import pandas as pd
        print("✓ 所有依赖包已安装")
    except ImportError as e:
        print(f"✗ 缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return
    
    # 运行测试
    tests_passed = 0
    total_tests = 2
    
    if test_basic_functionality():
        tests_passed += 1
    
    if test_data_saving():
        tests_passed += 1
    
    print(f"\n测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！爬虫可以正常使用。")
        print("\n运行完整爬虫:")
        print("python kaoyansou_crawler_enhanced.py")
    else:
        print("❌ 部分测试失败，请检查环境配置。")


if __name__ == "__main__":
    main()
