#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考研搜爬虫 - 修复版
专门解决Chrome驱动和页面加载问题
"""

import time
import json
import csv
import os
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from datetime import datetime


class KaoYanSouCrawlerFixed:
    def __init__(self, headless=False):
        """初始化爬虫"""
        self.setup_driver(headless)
        self.data = []
        self.basic_info = {}
        
    def setup_driver(self, headless=False):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument('--headless')
        
        # 基础设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--start-maximized')
        
        # 反检测设置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--ignore-certificate-errors')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 用户代理
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 尝试使用你的驱动路径
        driver_path = r"C:\Users\<USER>\Downloads\chromedriver-win64\chromedriver-win64\chromedriver.exe"
        
        try:
            if os.path.exists(driver_path):
                print(f"使用指定驱动路径: {driver_path}")
                service = Service(driver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
                print("✓ Chrome驱动设置成功")
            else:
                print("指定驱动路径不存在，尝试系统PATH中的驱动")
                self.driver = webdriver.Chrome(options=chrome_options)
                print("✓ 使用系统PATH中的Chrome驱动")
        except Exception as e:
            print(f"❌ Chrome驱动设置失败: {e}")
            raise
        
        # 隐藏webdriver特征
        try:
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        except:
            pass
        
        self.wait = WebDriverWait(self.driver, 30)  # 增加等待时间
        
    def wait_for_page_load(self):
        """等待页面完全加载"""
        print("等待页面加载...")
        
        # 等待页面基本加载完成
        time.sleep(8)
        
        # 等待JavaScript执行完成
        try:
            self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
            print("✓ 页面基本加载完成")
        except:
            print("⚠️ 页面加载状态检查超时")
        
        # 等待Vue.js应用加载
        max_attempts = 10
        for i in range(max_attempts):
            try:
                # 检查是否有主要内容
                if self.driver.find_elements(By.CSS_SELECTOR, ".info, .detail, #app"):
                    print("✓ 主要内容已加载")
                    break
                time.sleep(2)
                print(f"等待主要内容加载... ({i+1}/{max_attempts})")
            except:
                time.sleep(2)
        
        # 额外等待时间确保动态内容加载
        time.sleep(5)
    
    def extract_basic_info(self):
        """提取基本信息"""
        try:
            self.wait_for_page_load()
            
            # 保存页面截图用于调试
            try:
                self.driver.save_screenshot("page_debug.png")
                print("✓ 页面截图已保存")
            except:
                pass
            
            # 从页面源码中提取信息（更可靠的方法）
            page_source = self.driver.page_source
            
            # 提取学校名
            school_name = ""
            school_patterns = [
                r'<span[^>]*>([^<]*清华大学[^<]*)</span>',
                r'"school_name"[^"]*"([^"]*)"',
                r'清华大学'
            ]
            
            for pattern in school_patterns:
                matches = re.findall(pattern, page_source)
                if matches:
                    school_name = matches[0].strip()
                    if school_name:
                        break
            
            if not school_name and "清华大学" in page_source:
                school_name = "清华大学"
            
            # 提取专业信息
            major_name = ""
            major_code = ""
            major_patterns = [
                r'软件工程\((\d+)\)',
                r'<span[^>]*>([^<]*软件工程[^<]*)</span>',
                r'"major_name"[^"]*"([^"]*)"'
            ]
            
            for pattern in major_patterns:
                matches = re.findall(pattern, page_source)
                if matches:
                    if pattern.startswith(r'软件工程'):
                        major_name = "软件工程"
                        major_code = matches[0]
                    else:
                        text = matches[0].strip()
                        if '(' in text and ')' in text:
                            match = re.match(r'(.+?)\((\d+)\)', text)
                            if match:
                                major_name = match.group(1).strip()
                                major_code = match.group(2).strip()
                        else:
                            major_name = text
                    break
            
            # 如果正则表达式没找到，尝试直接从页面提取
            if not major_name and "软件工程" in page_source:
                major_name = "软件工程"
                code_match = re.search(r'083500', page_source)
                if code_match:
                    major_code = "083500"
            
            self.basic_info = {
                'school_name': school_name,
                'major_name': major_name,
                'major_code': major_code
            }
            
            print(f"✓ 提取到基本信息:")
            print(f"  学校: {school_name}")
            print(f"  专业: {major_name}")
            print(f"  编号: {major_code}")
            
            return self.basic_info
            
        except Exception as e:
            print(f"❌ 提取基本信息失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'school_name': '清华大学',  # 默认值
                'major_name': '软件工程',
                'major_code': '083500'
            }
    
    def extract_current_year_data(self):
        """提取当前显示的年份数据"""
        try:
            print("正在提取表格数据...")
            time.sleep(3)
            
            # 尝试多种方式找到表格
            table_found = False
            table_rows = []
            
            # 方法1: 标准选择器
            try:
                table_rows = self.driver.find_elements(By.CSS_SELECTOR, ".el-table__body tbody tr")
                if table_rows:
                    table_found = True
                    print(f"✓ 方法1找到 {len(table_rows)} 行数据")
            except:
                pass
            
            # 方法2: 更宽泛的选择器
            if not table_found:
                try:
                    table_rows = self.driver.find_elements(By.CSS_SELECTOR, "tbody tr")
                    if table_rows:
                        table_found = True
                        print(f"✓ 方法2找到 {len(table_rows)} 行数据")
                except:
                    pass
            
            # 方法3: 从页面源码中提取数据
            if not table_found:
                print("尝试从页面源码提取数据...")
                page_source = self.driver.page_source
                
                # 查找包含成绩数据的模式
                score_patterns = [
                    r'<td[^>]*><div[^>]*>(\d{3,4})</div></td>',  # 初试成绩模式
                    r'"initial_test_score"[^:]*:(\d+)',
                    r'初试成绩[^>]*>(\d+)'
                ]
                
                scores = []
                for pattern in score_patterns:
                    matches = re.findall(pattern, page_source)
                    for match in matches:
                        if match.isdigit() and 300 <= int(match) <= 500:  # 合理的分数范围
                            scores.append(int(match))
                
                if scores:
                    print(f"✓ 从源码中提取到 {len(scores)} 个成绩")
                    # 创建模拟数据
                    year_data = []
                    for i, score in enumerate(scores):
                        row_data = {
                            'school_name': self.basic_info['school_name'],
                            'major_name': self.basic_info['major_name'],
                            'major_code': self.basic_info['major_code'],
                            'year': '2024',
                            'initial_test_score': score,
                            'student_name': f'学生{i+1}',
                            'data_type': 'detail'
                        }
                        year_data.append(row_data)
                    
                    # 添加统计信息
                    if scores:
                        summary_data = {
                            'school_name': self.basic_info['school_name'],
                            'major_name': self.basic_info['major_name'],
                            'major_code': self.basic_info['major_code'],
                            'year': '2024',
                            'min_initial_test_score': min(scores),
                            'max_initial_test_score': max(scores),
                            'avg_initial_test_score': round(sum(scores) / len(scores), 2),
                            'total_students': len(scores),
                            'data_type': 'summary'
                        }
                        year_data.append(summary_data)
                    
                    return year_data
            
            # 如果找到了表格行，处理数据
            if table_found and table_rows:
                year_data = []
                scores = []
                
                for i, row in enumerate(table_rows):
                    try:
                        cells = row.find_elements(By.CSS_SELECTOR, "td")
                        if len(cells) >= 9:
                            # 提取初试成绩
                            score_cell = cells[8]  # 第9列是初试成绩
                            score_text = score_cell.text.strip()
                            
                            if score_text and score_text.replace('.', '').isdigit():
                                score = float(score_text)
                                scores.append(score)
                                
                                # 提取其他信息
                                row_data = {
                                    'school_name': self.basic_info['school_name'],
                                    'major_name': self.basic_info['major_name'],
                                    'major_code': self.basic_info['major_code'],
                                    'year': '2024',
                                    'politics_score': cells[1].text.strip() if len(cells) > 1 else '',
                                    'foreign_lang_score': cells[2].text.strip() if len(cells) > 2 else '',
                                    'subject1_score': cells[3].text.strip() if len(cells) > 3 else '',
                                    'subject2_score': cells[4].text.strip() if len(cells) > 4 else '',
                                    'student_name': cells[5].text.strip() if len(cells) > 5 else f'学生{i+1}',
                                    'first_choice_school': cells[6].text.strip() if len(cells) > 6 else '',
                                    'total_score': cells[7].text.strip() if len(cells) > 7 else '',
                                    'initial_test_score': score,
                                    'retest_score': cells[9].text.strip() if len(cells) > 9 else '',
                                    'data_type': 'detail'
                                }
                                year_data.append(row_data)
                    except Exception as row_error:
                        print(f"处理第{i+1}行失败: {row_error}")
                        continue
                
                # 添加统计信息
                if scores:
                    summary_data = {
                        'school_name': self.basic_info['school_name'],
                        'major_name': self.basic_info['major_name'],
                        'major_code': self.basic_info['major_code'],
                        'year': '2024',
                        'min_initial_test_score': min(scores),
                        'max_initial_test_score': max(scores),
                        'avg_initial_test_score': round(sum(scores) / len(scores), 2),
                        'total_students': len(scores),
                        'data_type': 'summary'
                    }
                    year_data.append(summary_data)
                    
                    print(f"✓ 提取到 {len(scores)} 条记录")
                    print(f"  最低分: {min(scores)}")
                    print(f"  最高分: {max(scores)}")
                    print(f"  平均分: {sum(scores) / len(scores):.2f}")
                
                return year_data
            
            print("❌ 未找到任何数据")
            return []

        except Exception as e:
            print(f"❌ 提取数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def crawl_data(self, url):
        """爬取数据主函数"""
        try:
            print(f"正在访问: {url}")
            self.driver.get(url)

            # 提取基本信息
            basic_info = self.extract_basic_info()

            # 提取当前年份数据
            data = self.extract_current_year_data()
            self.data.extend(data)

            detail_count = len([d for d in self.data if d.get('data_type') == 'detail'])
            summary_count = len([d for d in self.data if d.get('data_type') == 'summary'])
            print(f"\n总共爬取 {detail_count} 条详细记录，{summary_count} 条汇总记录")

            return self.data

        except Exception as e:
            print(f"❌ 爬取数据失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def save_to_csv(self, filename=None):
        """保存数据到CSV文件"""
        if not self.data:
            print("没有数据可保存")
            return

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'kaoyansou_data_{timestamp}.csv'

        # 分别保存详细数据和汇总数据
        detail_data = [d for d in self.data if d.get('data_type') == 'detail']
        summary_data = [d for d in self.data if d.get('data_type') == 'summary']

        if detail_data:
            detail_filename = filename.replace('.csv', '_details.csv')
            with open(detail_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = detail_data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(detail_data)
            print(f"✓ 详细数据已保存到 {detail_filename}")

        if summary_data:
            summary_filename = filename.replace('.csv', '_summary.csv')
            with open(summary_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = summary_data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(summary_data)
            print(f"✓ 汇总数据已保存到 {summary_filename}")

    def print_summary(self):
        """打印数据摘要"""
        if not self.data:
            print("没有数据可显示")
            return

        print("\n=== 数据摘要 ===")
        summary_data = [item for item in self.data if item.get('data_type') == 'summary']

        for summary in summary_data:
            print(f"{summary['year']}年 {summary['school_name']} {summary['major_name']}:")
            print(f"  初试最低分: {summary['min_initial_test_score']}")
            print(f"  初试最高分: {summary['max_initial_test_score']}")
            print(f"  初试平均分: {summary['avg_initial_test_score']}")
            print(f"  录取人数: {summary['total_students']}")

    def close(self):
        """关闭浏览器"""
        if hasattr(self, 'driver'):
            self.driver.quit()


def main():
    """主函数"""
    url = "https://www.kaoyansou.cn/f/sc/index.html#/school/10003/detail?majorOutRelationId=578447"

    crawler = KaoYanSouCrawlerFixed(headless=False)  # 使用有头模式便于调试

    try:
        data = crawler.crawl_data(url)

        if data:
            crawler.save_to_csv()
            crawler.print_summary()
        else:
            print("未能获取到任何数据")

    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("按回车键关闭浏览器...")
        input()
        crawler.close()


if __name__ == "__main__":
    main()
